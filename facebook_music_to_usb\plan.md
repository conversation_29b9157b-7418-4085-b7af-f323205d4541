# Facebook Music to USB Transfer Project Plan

## 用户需求分析
- **核心问题**: 用户想知道如何使用 Cinch Audio Recorder 从 Facebook 账户的"Saved items--music folder"复制音乐到 USB
- **目标受众**: 音乐爱好者，希望将在线音乐保存到便携设备
- **文章类型**: How-to 图文教程
- **预期字数**: 2300 words (根据info_aia.md要求)

## 执行步骤清单

### 第1步: 竞品研究与内容空白分析
- [ ] 搜索现有关于Facebook音乐下载的文章
- [ ] 分析竞品内容的不足之处
- [ ] 识别用户在Reddit/Quora等平台的未解决问题
- [ ] 找出独特价值点和信息增量

### 第2步: 创建超级大纲
- [ ] 提取参考URL的H2-H4标题
- [ ] 合并整理相似标题
- [ ] 按层级结构组织初步框架
- [ ] 保存为 super_outline.md

### 第3步: 生成最终大纲
- [ ] 基于超级大纲优化结构
- [ ] 添加字数分配 (总计2300字)
- [ ] 整合SEO长尾关键词
- [ ] 确保涵盖用户核心问题
- [ ] 保存为 final_outline.md

### 第4步: 撰写初稿
- [ ] 按照final_outline.md的字数要求撰写
- [ ] 遵循hl.md的人性化写作风格
- [ ] 整合Cinch Audio Recorder产品信息
- [ ] 添加相关图片和截图
- [ ] 保存为 first_draft.md

### 第5步: SEO优化
- [ ] 生成SEO标题和描述
- [ ] 优化关键词分布
- [ ] 添加内部和外部链接
- [ ] 保存为 seo_titles.md

## 完成标准
- [ ] 文章字数达到2300字要求
- [ ] 包含至少3个独特观点
- [ ] 每个H2章节都有相关图片
- [ ] 提供完整的Cinch使用步骤
- [ ] 包含Windows和Mac下载链接
- [ ] 通过E-E-A-T质量检查

## 预期输出文件
1. plan.md (本文件)
2. super_outline.md
3. final_outline.md  
4. first_draft.md
5. seo_titles.md

## 关键资源
- 参考文档: New_article/ref/car_guide.md
- 写作风格: New_article/hl.md
- 产品信息: Cinch Audio Recorder Pro
- 下载链接: Windows & Mac版本
- 专用图片资源: 已在car_guide.md中列出
