# Article Creation Plan: Download Apple Music to Shokz OpenSwim

## User Requirements and Goals
- **Topic**: Download Apple Music to Shokz OpenSwim
- **Target Length**: 1600 words (maximum 20% over = 1920 words)
- **Language**: English
- **Timeframe**: June 2025 content with latest data and trends
- **Audience**: Music lovers and creators focused on downloading, editing, and sharing music
- **Opening Strategy**: A (Surprising Statistic/Fact Opening)
- **SEO Keywords**: Download Apple Music to Shokz OpenSwim

## Quality Assessment Dimensions
1. **Effort**: Must show obvious human elements and deep thinking
2. **Originality**: Provide unique information increment, avoid rehashing existing content
3. **Talent/Skill**: Demonstrate professional knowledge and practical experience
4. **Accuracy**: Ensure factual accuracy, avoid misinformation

## Information Increment Requirements
- Include 3-5 unique viewpoints or solutions not covered by other articles
- Personal insights and trial-and-error stories based on actual usage experience
- Specific solutions for user pain points, not generic advice

## Detailed Execution Steps

### Step 1: Extract Requirements ✅
- Read and extract all requirements from `New_article/info_aia.md`
- Record requirements clearly to guide subsequent steps
- Confirm all requirements are understood and incorporated into plan

### Step 2: Generate Outline
- Follow `New_article/outline.md` workflow to create:
  - Initial outline saved as `super_outline.md`
  - Final outline saved as `final_outline.md`

### Step 3: Create First Draft
- Use final outline (`final_outline.md`) following `New_article/first_draft.md` workflow
- Write first draft and save as `first_draft.md`

### Step 4: Generate SEO Content
- Follow `New_article/seo_titles.md` workflow to create:
  - SEO titles and corresponding meta descriptions
  - Featured image prompts for production articles
  - Save all SEO-related content to `seo_metadata_images.md`

### Step 5: Quality Check
- Verify word count is precisely controlled within user requirements
- Check for obvious AI language and sentence structures
- Verify internal and external link quantities meet standards

## Completion Standards and Checkpoints

### Outline Phase
- [ ] Super outline extracted from reference URLs
- [ ] Final outline optimized with word count allocation
- [ ] Content gaps identified from competitor analysis
- [ ] Unique value points prepared for each section

### Draft Phase
- [ ] Word count within 1600-1920 range
- [ ] Human writing style with personal experiences
- [ ] Cinch Audio Recorder properly integrated
- [ ] Internal and external links included

### SEO Phase
- [ ] SEO titles and meta descriptions created
- [ ] Featured image prompts generated
- [ ] Long-tail keywords identified

## Expected Output Files
1. `plan.md` - This execution plan
2. `super_outline.md` - Initial outline from reference URLs
3. `final_outline.md` - Optimized final outline with word allocation
4. `first_draft.md` - Complete article draft
5. `seo_metadata_images.md` - SEO content and image prompts

## Recommended Product Integration
- **Product**: Cinch Audio Recorder Pro
- **Price**: $25.99 USD (use as value point, don't include in introduction)
- **Reference**: All product information from `New_article/ref/car_guide.md`
- **Download Links**: Windows and Mac versions available
- **Integration Strategy**: Follow modular structure from info_aia.md guidelines

## Reference URLs for Content Research
1. https://www.drmare.com/apple-music/download-apple-music-to-shokz-openswim.html
2. https://www.audfree.com/apple-music-tips/play-apple-music-on-shokz-openswim.html
3. https://www.facebook.com/groups/5848236027/posts/10161630481971028/
4. https://www.tiktok.com/discover/how-to-download-music-to-shokz-openswim

## Success Metrics
- Article provides 3-5 unique insights not found in competitor articles
- Word count precisely controlled within target range
- Human writing style with personal experiences integrated
- Proper product recommendation placement and integration
- SEO optimization with relevant keywords and meta content
